import React from 'react';

const KeywordTags = ({tags}:any) => {
  // Filter out any invalid tags and ensure we have strings
  const validTags = tags?.filter((tag: any) => {
    if (!tag) return false;
    if (typeof tag === 'string') return tag.trim() !== '';
    if (typeof tag === 'object' && tag.value) return tag.value.toString().trim() !== '';
    return tag.toString().trim() !== '';
  }) || [];

  if (!validTags.length) {
    return <span className="!text-foreground !font-medium !text-[14px]">N/A</span>;
  }

  return (
    <div className="flex flex-wrap gap-1 items-center">
      {validTags.map((tag: any, index: number) => (
        <span
          key={`tag-${index}`}
          className="inline-flex items-center px-2 py-1 rounded-[6px] border border-input bg-primary-light text-foreground text-[12px] font-medium"
        >
          {typeof tag === 'string' ? tag : (tag?.value || tag.toString())}
        </span>
      ))}
    </div>
  );
};

export default KeywordTags;