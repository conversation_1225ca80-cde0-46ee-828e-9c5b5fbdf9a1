import CodeGenerator<PERSON>elper from "~/modules/codeGenerator/utils/CodeGeneratorHelper";
import CodeGeneratorPropertiesHelper from "~/modules/codeGenerator/utils/CodeGeneratorPropertiesHelper";
import { EntityWithDetails } from "~/utils/db/entities/entities.db.server";
import { PropertyType } from "~/application/enums/entities/PropertyType";

function generate({ entity }: { entity: EntityWithDetails }): string {
  const { capitalized } = CodeGeneratorHelper.getNames(entity);
  const imports: string[] = [
    `import { useTranslation } from "react-i18next";
import { ReactNode } from "react";
import clsx from "clsx";
import { PropertyType } from "~/application/enums/entities/PropertyType";
import { ${capitalized}Dto } from "../dtos/${capitalized}Dto";`
  ];

  let template = `
export default function ${capitalized}Overview({ item }: { item?: ${capitalized}Dto }) {
  const { t } = useTranslation();

  // Define field groups
  const fieldGroups = [
    {
      title: t("shared.details"),
      fields: [
        {PROPERTIES_FIELDS}
      ],
    },
  ];

  // Explicitly define the return type as ReactNode to handle all possible value types
  const getValue = (field: any): ReactNode => {
    if (!item) return "N/A";

    const value = item[field.name as keyof ${capitalized}Dto];
    if (value === undefined || value === null) return "N/A";

    switch (field.type) {
      case PropertyType.BOOLEAN:
        return value ? "Yes" : "No";

      case PropertyType.DATE:
        return formatDate(value as string);

      case PropertyType.SELECT:
        return value?.toString() || "N/A";

      case PropertyType.MULTI_SELECT:
        if (Array.isArray(value)) {
          return value.map(v => v?.value || v?.toString()).join(", ") || "N/A";
        }
        return "N/A";

      case PropertyType.RANGE_NUMBER:
        if (value && typeof value === "object" && "numberMin" in value && "numberMax" in value) {
          const range = value as { numberMin?: number | null; numberMax?: number | null };
          const min = typeof range.numberMin === "number" ? range.numberMin : "N/A";
          const max = typeof range.numberMax === "number" ? range.numberMax : "N/A";
          return \`\${min} - \${max}\`;
        }
        return "N/A";

      case PropertyType.RANGE_DATE:
        if (value && typeof value === "object" && "dateMin" in value && "dateMax" in value) {
          const dateRange = value as { dateMin?: string; dateMax?: string };
          return \`\${formatDate(dateRange.dateMin) || "N/A"} - \${formatDate(dateRange.dateMax) || "N/A"}\`;
        }
        return "N/A";

      case PropertyType.MULTI_TEXT:
        if (Array.isArray(value)) {
          // Handle RowValueMultipleDto[] - extract the value property
          return value.map(v => v?.value || v?.toString()).filter(Boolean).join(", ") || "N/A";
        } else if (typeof value === "string") {
          return value || "N/A";
        }
        return "N/A";

      case PropertyType.MEDIA:
        if (Array.isArray(value)) {
          return value.length > 0 ? \`\${value.length} file(s)\` : "N/A";
        }
        return "N/A";

      case PropertyType.TEXT:
        if (field.editor === "wysiwyg" && typeof value === "string") {
          return <div dangerouslySetInnerHTML={{ __html: value }} />;
        }
        return value?.toString() || "N/A";

      default:
        return value?.toString() || "N/A";
    }
  };

  const isFullWidthField = (type: PropertyType, editor?: string) => {
    return type === PropertyType.MEDIA ||
      type === PropertyType.BOOLEAN ||
      editor === "monaco" ||
      editor === "wysiwyg";
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (e) {
      return "Invalid Date";
    }
  };

  return (
    <div className="space-y-6">
      {fieldGroups.map((group, groupIndex) => (
        <div key={groupIndex} className="flex max-w-full flex-col overflow-hidden rounded-[8px] border border-solid border-input bg-card">
          <section className="w-full self-center text-sm max-md:max-w-full px-5 pt-5 pb-4">
            <h2 className="self-start text-foreground font-bold text-[14px] leading-[100%] tracking-[0%]">
              {group.title}
            </h2>
          </section>

          <section className="flex w-full flex-wrap items-center gap-6 self-center px-5 text-sm max-md:max-w-full pb-5">
            {group.fields.map((field) => {
              const dynamicClass = clsx(
                isFullWidthField(field.type, field.editor) ? "w-full" : "min-w-[300px]"
              );

              return (
                <div key={field.name} className={dynamicClass}>
                  <div className="text-muted-foreground text-xs font-medium mb-1">
                    {t(field.title)}
                  </div>
                  <div className="text-foreground text-sm">
                    {getValue(field)}
                  </div>
                </div>
              );
            })}
          </section>
        </div>
      ))}
    </div>
  );
}`;

  // Generate property fields
  const propertyFields: string[] = [];
  entity.properties
    .filter(f => !f.isDefault)
    .forEach(property => {
      let editorAttr = "";
      if (property.type === PropertyType.TEXT) {
        const editor = property.attributes.find(a => a.name === "editor")?.value;
        if (editor) {
          editorAttr = `, editor: "${editor}"`;
        }
      }
      propertyFields.push(`{ name: "${property.name}", title: "${property.title}", type: PropertyType.${PropertyType[property.type]}${editorAttr} }`);
    });
  
  template = template.replace("{PROPERTIES_FIELDS}", propertyFields.join(",\n        "));

  const uniqueImports = [...new Set(imports)];
  return [...uniqueImports, template].join("\n");
}

export default {
  generate,
};

