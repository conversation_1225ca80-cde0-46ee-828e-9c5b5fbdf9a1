{"version": 3, "sources": ["../../remix-i18next/src/react.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { useMatches } from \"react-router\";\n\n/**\n * Get the locale returned by the root route loader under the `locale` key.\n * @example\n * let locale = useLocale()\n * let formattedDate = date.toLocaleDateString(locale);\n * @example\n * let locale = useLocale(\"language\")\n * let formattedDate = date.toLocaleDateString(locale);\n */\nexport function useLocale(localeKey = \"locale\"): string {\n\tlet matches = useMatches();\n\t// biome-ignore lint/style/noNonNullAssertion: There's always a root match\n\tlet rootMatch = matches[0]!;\n\tlet { [localeKey]: locale } =\n\t\t(rootMatch.data as Record<string, string>) ?? {};\n\tif (!locale) throw new Error(\"Missing locale returned by the root loader.\");\n\tif (typeof locale === \"string\") return locale;\n\tthrow new Error(\"Invalid locale returned by the root loader.\");\n}\n\n/**\n * Detect when the locale returned by the root route loader changes and call\n * `i18n.changeLanguage` with the new locale.\n * This will ensure translations are loaded automatically.\n */\nexport function useChangeLanguage(locale: string) {\n\tlet { i18n } = useTranslation();\n\tReact.useEffect(() => {\n\t\tif (i18n.language !== locale) i18n.changeLanguage(locale);\n\t}, [locale, i18n]);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,YAAuB;AAajB,SAAU,UAAU,YAAY,UAAQ;AAC7C,MAAI,UAAU,WAAU;AAExB,MAAI,YAAY,QAAQ,CAAC;AACzB,MAAI,EAAE,CAAC,SAAS,GAAG,OAAM,IACvB,UAAU,QAAmC,CAAA;AAC/C,MAAI,CAAC;AAAQ,UAAM,IAAI,MAAM,6CAA6C;AAC1E,MAAI,OAAO,WAAW;AAAU,WAAO;AACvC,QAAM,IAAI,MAAM,6CAA6C;AAC9D;AAOM,SAAU,kBAAkB,QAAc;AAC/C,MAAI,EAAE,KAAI,IAAK,eAAc;AAC7B,EAAM,gBAAU,MAAK;AACpB,QAAI,KAAK,aAAa;AAAQ,WAAK,eAAe,MAAM;EACzD,GAAG,CAAC,QAAQ,IAAI,CAAC;AAClB;", "names": []}