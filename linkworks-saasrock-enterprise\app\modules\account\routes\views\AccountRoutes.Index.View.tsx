// Route View (Client component): Table with rows and quick row overview
// Date: 2025-07-31
// Version: SaasRock v1.6.0

import { useActionData, useLoaderData, useSearchParams, Link } from "react-router";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import RowCreatedByCell from "~/components/entities/rows/cells/RowCreatedByCell";
import RowFolioCell from "~/components/entities/rows/cells/RowFolioCell";
import ButtonPrimary from "~/components/ui/buttons/ButtonPrimary";
import DateCell from "~/components/ui/dates/DateCell";
import ExternalLinkEmptyIcon from "~/components/ui/icons/ExternalLinkEmptyIcon";
import InputFilters from "~/components/ui/input/InputFilters";
import SlideOverWideEmpty from "~/components/ui/slideOvers/SlideOverWideEmpty";
import AccountForm from "../../components/AccountForm";
import { AccountDto } from "../../dtos/AccountDto";
import { AccountRoutesIndexApi } from "../api/AccountRoutes.Index.Api";
import TableSearch from "~/custom/components/tables/TableSearch";
import DownloadCSVButton from "~/custom/components/Buttons/DownloadCSVButton";
import AddButton from "~/custom/components/tables/AddButton";
import { FilterablePropertyDto } from "~/application/dtos/data/FilterablePropertyDto";
import DataTable from "~/custom/components/DataTable";
import RowSelectedOptionCell from "~/components/entities/rows/cells/RowSelectedOptionCell";
import { Colors } from "~/application/enums/shared/Colors";
import RowNumberCell from "~/components/entities/rows/cells/RowNumberCell";
import RowDateCell from "~/components/entities/rows/cells/RowDateCell";
import PropertyMultipleValueBadge from "~/components/entities/properties/PropertyMultipleValueBadge";
import RowBooleanCell from "~/components/entities/rows/cells/RowBooleanCell";

export default function AccountRoutesIndexView() {
  const { t } = useTranslation();
  const data = useLoaderData<AccountRoutesIndexApi.LoaderData>();
  const actionData = useActionData<{ error?: string; success?: string }>();
  const [searchParams, setSearchParams] = useSearchParams();

  const [overviewItem, setOverviewItem] = useState<AccountDto>();
  const [searchInput, setSearchInput] = useState<string>("");
  const [filters, setFilters] = useState<FilterablePropertyDto[]>([]);
  const [opened, setOpened] = useState(false);

  const handleFilter = () => {
    setOpened(!opened);
  };

  useEffect(() => {
    setFilters(data.filterableProperties ?? []);
  }, [data.filterableProperties]);

  function getSearchableKeys(item: AccountDto): (keyof AccountDto)[] {
    return Object.keys(item).filter(key => {
      const value = item[key as keyof AccountDto];
      return (
        typeof value === "string" ||
        typeof value === "number" ||
        typeof value === "boolean" ||
        value instanceof Date
      );
    }) as (keyof AccountDto)[];
  }

  function filteredItems() {
    const { items } = data;
    const input = searchInput?.toLowerCase().trim();
    if (!input) return items;
    if (items.length === 0) return [];

    const searchableKeys = getSearchableKeys(items[0]);
    return items.filter(item =>
      searchableKeys.some(key => {
        const v = item[key];
        if (v == null) return false;

        if (Array.isArray(v)) {
          return v.some(e => {
            if (typeof e === "string" || typeof e === "number") {
              return String(e).toLowerCase().includes(input);
            }
            if ("value" in e && typeof e.value === "string") {
              return e.value.toLowerCase().includes(input);
            }
            return false;
          });
        }

        if (v instanceof Date) {
          return v.toISOString().toLowerCase().includes(input);
        }

        return String(v).toLowerCase().includes(input);
      })
    );
  }


  return (
     <div className="mx-auto sticky ">
      <div className="flex flex-col ">
        <div className="flex h-[76px] items-center justify-between gap-2 px-3 ">
          <div className="ml-3 flex flex-row items-center justify-between gap-3 text-lg leading-none font-bold tracking-normal max-[769px]:flex-col max-[769px]:items-start">
            <h3 className="text-secondary-foreground flex flex-1 items-center truncate text-lg font-bold">{t("Accounts")}</h3>
          </div>
          <div className="flex w-full min-w-0 flex-wrap items-center justify-end gap-x-2 gap-y-2 overflow-x-auto px-4 py-2 ">
            <TableSearch value={searchInput} setValue={setSearchInput} className="flex-shrink-0" />
            <DownloadCSVButton rowsData={data.items} searchParams={searchParams.toString()} className="flex-shrink-0" />
            
            {filters.length > 0 && <InputFilters filters={filters} Opened={opened} handleFilter={handleFilter} className="flex-shrink-0" />}
            <AddButton
              label={<span className="sm:text-sm">{t("shared.addNew")} {t("Accounts")}</span>}
              className="custom-class-for-add-button"

              to="new"
            />
          </div>
        </div>
   
      <DataTable
        items={filteredItems()}
        filters={filters}
        opened={opened}
        setOpened={setOpened}
        handleFilter={handleFilter}
        pagination={data.pagination}
        actions={[
          {
            title: t("shared.overview"),
            onClick: (_, item) => {
              searchParams.set("overview", item.row.id);
              setSearchParams(searchParams);
            },
          },
          {
            title: t("shared.edit"),
            onClickRoute: (_, item) => item.row.id,
          },
        ]}
        headers={[
          {
            name: "folio",
            title: t("models.row.folio"),
            value: (item) => <RowFolioCell prefix={item.prefix} folio={item.row.folio} href={item.row.id} />,
          },
          {
            name: "domainName",
            title: t("Domain Name"),
            value: (item) => <div className="max-w-sm truncate">{item.domainName}</div>,
          },
          {
            name: "name",
            title: t("Company Name "),
            value: (item) => <div className="max-w-sm truncate">{item.name}</div>,
          },
          {
            name: "accounttype",
            title: t("Account Type"),
            value: (item) => <RowSelectedOptionCell value={item?.accounttype} display="Value" options={[{ name: null, value: "Client ", color: Colors.UNDEFINED },{ name: null, value: "Recruiter ", color: Colors.UNDEFINED },{ name: null, value: "Partner ", color: Colors.UNDEFINED }]} />,
          },
          {
            name: "website",
            title: t("Website "),
            value: (item) => <div className="max-w-sm truncate">{item.website}</div>,
          },
          {
            name: "industry",
            title: t("Industry "),
            value: (item) => <div className="max-w-sm truncate">{item.industry}</div>,
          },
          {
            name: "subIndustry",
            title: t("Sub Industry "),
            value: (item) => <div className="max-w-sm truncate">{item.subIndustry}</div>,
          },
          {
            name: "user",
            title: t("Account Owner"),
            value: (item) => <RowSelectedOptionCell value={item?.user} display="Value" options={[]} />,
          },
          {
            name: "about",
            title: t("About"),
            value: (item) => <div className="max-w-sm truncate">{item.about}</div>,
          },
          {
            name: "logo",
            title: t("Logo"),
            value: (item) => <div className="max-w-sm truncate">{item.logo}</div>,
          },
          {
            name: "foundingYear",
            title: t("Founding Year "),
            value: (item) => <RowNumberCell value={item.foundingYear} format="integer" />,
          },
          {
            name: "size",
            title: t("Size of Company"),
            value: (item) => <RowNumberCell value={item.size} format="integer" />,
          },
          {
            name: "contractEndDate",
            title: t("Contract End Date "),
            value: (item) => <RowDateCell value={item.contractEndDate} format="DD-MM-YYYY" />,
          },
          {
            name: "currency",
            title: t("Currency"),
            value: (item) => <RowSelectedOptionCell value={item?.currency} display="Value" options={[{ name: null, value: "INR (₹)", color: Colors.UNDEFINED },{ name: null, value: "USD ($)", color: Colors.UNDEFINED },{ name: null, value: "EUR (€)", color: Colors.UNDEFINED },{ name: null, value: "ZAR (R)", color: Colors.UNDEFINED }]} />,
          },
          {
            name: "revenueRange",
            title: t("Revenue Range"),
            value: (item) => <div className="max-w-sm truncate">{item.revenueRange}</div>,
          },
          {
            name: "isParentCompany",
            title: t("Is Parent Company"),
            value: (item) => <RowSelectedOptionCell value={item?.isParentCompany} display="Value" options={[{ name: null, value: "Yes", color: Colors.UNDEFINED },{ name: null, value: "No", color: Colors.UNDEFINED }]} />,
          },
          {
            name: "parentCompanyName",
            title: t("Parent Company Name"),
            value: (item) => <div className="max-w-sm truncate">{item.parentCompanyName}</div>,
          },
          {
            name: "subsidiaries",
            title: t("Subsidiaries"),
            value: (item) => <PropertyMultipleValueBadge values={item.subsidiaries} options={[]} />,
          },
          {
            name: "linkedinUrl",
            title: t("Linkedin"),
            value: (item) => <div className="max-w-sm truncate">{item.linkedinUrl}</div>,
          },
          {
            name: "twitterprofileUrl",
            title: t("Twitter"),
            value: (item) => <div className="max-w-sm truncate">{item.twitterprofileUrl}</div>,
          },
          {
            name: "instagram",
            title: t("Instagram"),
            value: (item) => <div className="max-w-sm truncate">{item.instagram}</div>,
          },
          {
            name: "facebookProfileUrl",
            title: t("Facebook"),
            value: (item) => <div className="max-w-sm truncate">{item.facebookProfileUrl}</div>,
          },
          {
            name: "crunchbase",
            title: t("Crunchbase"),
            value: (item) => <div className="max-w-sm truncate">{item.crunchbase}</div>,
          },
          {
            name: "technologyStack",
            title: t("Technology Stack"),
            value: (item) => <PropertyMultipleValueBadge values={item.technologyStack} options={[]} />,
            
          },
          {
            name: "source",
            title: t("Source"),
            value: (item) => <RowSelectedOptionCell value={item?.source} display="Value" options={[{ name: null, value: "Apollo", color: Colors.UNDEFINED },{ name: null, value: "Manual", color: Colors.UNDEFINED },{ name: null, value: "ZoomInfo", color: Colors.UNDEFINED },{ name: null, value: "LinkedIn", color: Colors.UNDEFINED }]} />,
          },
          {
            name: "confidenceScore",
            title: t("Confidence Score"),
            value: (item) => <RowNumberCell value={item.confidenceScore} format="decimal" />,
          },
          {
            name: "verified",
            title: t("Verified"),
            value: (item) => <RowBooleanCell value={item.verified} format="yesNo" />,
          },
          {
            name: "createdAt",
            title: t("shared.createdAt"),
            value: (item) => <DateCell date={item.row.createdAt} />,
            className: "text-muted-foreground text-xs",
            breakpoint: "sm",
          },
          {
            name: "createdByUser",
            title: t("shared.createdBy"),
            value: (item) => <RowCreatedByCell user={item.row.createdByUser} apiKey={item.row.createdByApiKey} />,
            className: "text-muted-foreground text-xs",
            breakpoint: "sm",
          },
        ]}
      />
      <SlideOverWideEmpty
        withTitle={false}
        withClose={false}
        title={t("Account")}
        open={!!searchParams.get("overview")?.toString()}
        onClose={() => {
          searchParams.delete("overview");
          setSearchParams(searchParams);
          setTimeout(() => {
            setOverviewItem(undefined);
          }, 100);
        }}
        className="sm:max-w-md"
        buttons={
          <>
            <Link
              to={overviewItem?.row.id ?? ""}
              className="rounded-md bg-background text-muted-foreground hover:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              <span className="sr-only">Close panel</span>
              <ExternalLinkEmptyIcon className="h-6 w-6" aria-hidden="true" />
            </Link>
          </>
        }
      >
        {!overviewItem ? <div>{t("shared.loading")}...</div> : <AccountForm item={overviewItem} actionData={actionData} />}
      </SlideOverWideEmpty>
      </div>
    </div>
    
  );
}
