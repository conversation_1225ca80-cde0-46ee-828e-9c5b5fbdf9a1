import {
  useTranslation
} from "./chunk-LOD4OMZL.js";
import "./chunk-J3UXB5RM.js";
import {
  useMatches
} from "./chunk-MVZ6L74X.js";
import "./chunk-AUXUJC4C.js";
import "./chunk-NA32P3ZC.js";
import {
  require_react
} from "./chunk-R26XTA6N.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/remix-i18next/build/react.js
var React = __toESM(require_react());
function useLocale(localeKey = "locale") {
  let matches = useMatches();
  let rootMatch = matches[0];
  let { [localeKey]: locale } = rootMatch.data ?? {};
  if (!locale)
    throw new Error("Missing locale returned by the root loader.");
  if (typeof locale === "string")
    return locale;
  throw new Error("Invalid locale returned by the root loader.");
}
function useChangeLanguage(locale) {
  let { i18n } = useTranslation();
  React.useEffect(() => {
    if (i18n.language !== locale)
      i18n.changeLanguage(locale);
  }, [locale, i18n]);
}
export {
  useChangeLanguage,
  useLocale
};
//# sourceMappingURL=remix-i18next_react.js.map
