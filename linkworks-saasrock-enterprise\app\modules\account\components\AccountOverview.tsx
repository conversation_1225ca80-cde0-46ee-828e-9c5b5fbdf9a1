// Component: Overview with reading, states
// Date: 2025-07-31
// Version: SaasRock v1.6.0

import { useTranslation } from "react-i18next";
import { ReactNode } from "react";
import clsx from "clsx";
import { PropertyType } from "~/application/enums/entities/PropertyType";
import { AccountDto } from "../dtos/AccountDto";

export default function AccountOverview({ item }: { item?: AccountDto }) {
  const { t } = useTranslation();

  // Define field groups
  const fieldGroups = [
    {
      key: 'basicInfo',
      title: "Basic Information",
      fields: [
        { name: "domainName", title: "Domain Name", type: PropertyType.TEXT },
        { name: "name", title: "Company Name ", type: PropertyType.TEXT },
        { name: "accounttype", title: "Account Type", type: PropertyType.SELECT },
        { name: "website", title: "Website ", type: PropertyType.TEXT },
        { name: "industry", title: "Industry ", type: PropertyType.TEXT },
        { name: "subIndustry", title: "Sub Industry ", type: PropertyType.TEXT },
        { name: "user", title: "Account Owner", type: PropertyType.SELECT },
        { name: "about", title: "About", type: PropertyType.TEXT },
        { name: "logo", title: "Logo", type: PropertyType.TEXT },
      ],
    },
    {
      key: 'companySize',
      title: "Company Size & Lifecycle",
      fields: [
        { name: "foundingYear", title: "Founding Year ", type: PropertyType.NUMBER },
        { name: "size", title: "Size of Company", type: PropertyType.NUMBER },
        { name: "contractEndDate", title: "Contract End Date ", type: PropertyType.DATE },
        { name: "currency", title: "Currency", type: PropertyType.SELECT },
        { name: "revenueRange", title: "Revenue Range", type: PropertyType.TEXT },
        { name: "isParentCompany", title: "Is Parent Company", type: PropertyType.SELECT },
        { name: "parentCompanyName", title: "Parent Company Name", type: PropertyType.TEXT },
        { name: "subsidiaries", title: "Subsidiaries", type: PropertyType.MULTI_TEXT },
      ],
    },
    {
      key: 'socialLinks',
      title: "Social & Web Links",
      fields: [
        { name: "linkedinUrl", title: "Linkedin", type: PropertyType.TEXT },
        { name: "twitterprofileUrl", title: "Twitter", type: PropertyType.TEXT },
        { name: "instagram", title: "Instagram", type: PropertyType.TEXT },
        { name: "facebookProfileUrl", title: "Facebook", type: PropertyType.TEXT },
        { name: "crunchbase", title: "Crunchbase", type: PropertyType.TEXT },
      ],
    },
    {
      key: 'technographics',
      title: "Technographics",
      fields: [
        { name: "technologyStack", title: "Technology Stack", type: PropertyType.MULTI_TEXT },
      ],
    },
    {
      key: 'dataManagement',
      title: "Data Management",
      fields: [
        { name: "source", title: "Source", type: PropertyType.SELECT },
        { name: "confidenceScore", title: "Confidence Score", type: PropertyType.NUMBER },
        { name: "verified", title: "Verified", type: PropertyType.BOOLEAN }
      ],
    },
  ];



  // Explicitly define the return type as ReactNode to handle all possible value types
  const getValue = (field: any): ReactNode => {
    if (!item) return "N/A";

    const value = item[field.name as keyof AccountDto];
    if (value === undefined || value === null) return "N/A";

    switch (field.type) {
      case PropertyType.BOOLEAN:
        return value ? "Yes" : "No";

      case PropertyType.DATE:
        return formatDate(value as string);

      case PropertyType.SELECT:
        return value?.toString() || "N/A";

      case PropertyType.MULTI_SELECT:
        if (Array.isArray(value)) {
          return value.map(v => v?.value || v?.toString()).join(", ") || "N/A";
        }
        return "N/A";

      case PropertyType.RANGE_NUMBER:
        if (value && typeof value === "object" && "numberMin" in value && "numberMax" in value) {
          const range = value as { numberMin?: number | null; numberMax?: number | null };
          const min = typeof range.numberMin === "number" ? range.numberMin : "N/A";
          const max = typeof range.numberMax === "number" ? range.numberMax : "N/A";
          return `${min} - ${max}`;
        }
        return "N/A";

      case PropertyType.RANGE_DATE:
        if (value && typeof value === "object" && "dateMin" in value && "dateMax" in value) {
          const dateRange = value as { dateMin?: string; dateMax?: string };
          return `${formatDate(dateRange.dateMin) || "N/A"} - ${formatDate(dateRange.dateMax) || "N/A"}`;
        }
        return "N/A";

      case PropertyType.MULTI_TEXT:
        if (Array.isArray(value)) {
          // Handle RowValueMultipleDto[] - extract the value property
          return value.map(v => v?.value || v?.toString()).filter(Boolean).join(", ") || "N/A";
        } else if (typeof value === "string") {
          return value || "N/A";
        }
        return "N/A";

      case PropertyType.MEDIA:
        if (Array.isArray(value)) {
          return value.length > 0 ? `${value.length} file(s)` : "N/A";
        }
        return "N/A";

      case PropertyType.TEXT:
        if (field.editor === "wysiwyg" && typeof value === "string") {
          return <div dangerouslySetInnerHTML={{ __html: value }} />;
        }
        return value?.toString() || "N/A";

      default:
        return value?.toString() || "N/A";
    }
  };

  const isFullWidthField = (type: PropertyType) => {
    return type === PropertyType.MEDIA ||
      type === PropertyType.BOOLEAN;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (e) {
      return "Invalid Date";
    }
  };

  return (
    <div className="space-y-6">
      {fieldGroups.map((group, groupIndex) => (
        <div key={groupIndex} className="flex max-w-full flex-col overflow-hidden rounded-[8px] border border-solid border-input bg-card">
          <section className="w-full self-center text-sm max-md:max-w-full px-5 pt-5 pb-4">
            <h2 className="self-start text-foreground font-bold text-[14px] leading-[100%] tracking-[0%]">
              {group.title}
            </h2>
          </section>

          <section className="flex w-full flex-wrap items-center gap-6 self-center px-5 text-sm max-md:max-w-full pb-5">
            {group.fields.map((field) => {
              const dynamicClass = clsx(
                isFullWidthField(field.type) ? "w-full" : "min-w-[300px]"
              );

              return (
                <div key={field.name} className={dynamicClass}>
                  <div className="text-muted-foreground text-xs font-medium mb-1">
                    {t(field.title)}
                  </div>
                  <div className="text-foreground text-sm">
                    {getValue(field)}
                  </div>
                </div>
              );
            })}
          </section>
        </div>
      ))}
    </div>
  );
}