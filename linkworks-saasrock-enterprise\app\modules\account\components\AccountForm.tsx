// Component: Form with creating, reading, updating, and deleting states
// Date: 2025-07-31
// Version: SaasRock v1.6.0

import { useNavigation, useSubmit, Form } from "react-router";
import { useTranslation } from "react-i18next";
import { useRef, useState, useEffect } from "react";
import ButtonPrimary from "~/components/ui/buttons/ButtonPrimary";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";

import ActionResultModal from "~/components/ui/modals/ActionResultModal";
import ConfirmModal, { RefConfirmModal } from "~/components/ui/modals/ConfirmModal";
import { AccountDto } from "../dtos/AccountDto";
import InputText from "~/components/ui/input/InputText";
import { Colors } from "~/application/enums/shared/Colors";
import InputSelect from "~/components/ui/input/InputSelect";
import InputTextSubtype from "~/components/ui/input/subtypes/InputTextSubtype";
import InputNumber from "~/components/ui/input/InputNumber";
import InputDate from "~/components/ui/input/InputDate";
import InputMultiText from "~/components/ui/input/InputMultiText";
import InputCheckbox from "~/components/ui/input/InputCheckbox";
import { RowValueMultipleDto } from "~/application/dtos/entities/RowValueMultipleDto";
import InputGroup from "~/components/ui/forms/InputGroup";

// Interface for Apollo API response structure
interface ScrapAgentResponse {
  basicInformation: {
    account_id: string;
    external_id: string;
    account_type: string;
    company_name: string;
    website_url: string;
    industry: string;
    sub_industry: string;
    about: string;
    logo: string;
  };
  locationInformation: {
    location_id: string;
    address: string;
    city: string;
    state: string;
    country: string;
    primary_phone: string;
    is_hq: string;
    location_type: string;
  };
  companyInfo: {
    size_of_company: number;
    founding_year: number;
    revenue_range: string;
    employee_range_text: string;
    is_parent_company: boolean;
    parent_company: string;
    subsidiaries: string[];
    contract_end_date: string;
  };
  technographics: string[];
  socialLinks: {
    linkedin_url: string;
    twitter_url: string;
    facebook_url: string;
    instagram_url: string;
    crunchbase_url: string;
  };
}



export default function AccountForm({
  item,
  actionData,
  isUpdating,
  isCreating,
  canUpdate,
  canDelete,
  onCancel,
}: {
  item?: AccountDto;
  actionData: { success?: string; error?: string } | undefined;
  isUpdating?: boolean;
  isCreating?: boolean;
  canUpdate?: boolean;
  canDelete?: boolean;
  onCancel?: () => void;
}) {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const submit = useSubmit();
  const confirmDelete = useRef<RefConfirmModal>(null);

  // Form state management for controlled inputs - ALL FIELDS
  const [formData, setFormData] = useState({
    domainName: item?.domainName || "",
    name: item?.name || "",
    accounttype: item?.accounttype || "",
    website: item?.website || "",
    industry: item?.industry || "",
    subIndustry: item?.subIndustry || "",
    user: item?.user || "",
    about: item?.about || "",
    logo: item?.logo || "",
    foundingYear: item?.foundingYear,
    size: item?.size,
    contractEndDate: item?.contractEndDate,
    currency: item?.currency || "USD ($)",
    revenueRange: item?.revenueRange || "",
    isParentCompany: item?.isParentCompany || "",
    parentCompanyName: item?.parentCompanyName || "",
    subsidiaries: item?.subsidiaries || [] as RowValueMultipleDto[],
    linkedinUrl: item?.linkedinUrl || "",
    twitterprofileUrl: item?.twitterprofileUrl || "",
    instagram: item?.instagram || "",
    facebookProfileUrl: item?.facebookProfileUrl || "",
    crunchbase: item?.crunchbase || "",
    technologyStack: item?.technologyStack || [] as RowValueMultipleDto[],
    source: item?.source || "",
    confidenceScore: item?.confidenceScore,
    verified: item?.verified || false,
  });

  // API loading states
  const [isLoadingAPI, setIsLoadingAPI] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);

  // Debounced domain input to prevent typing interruption
  const [domainInputValue, setDomainInputValue] = useState(formData.domainName || "");
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  // Update form data when item prop changes
  useEffect(() => {
    if (item) {
      setFormData({
        domainName: item.domainName || "",
        name: item.name || "",
        accounttype: item.accounttype || "",
        website: item.website || "",
        industry: item.industry || "",
        subIndustry: item.subIndustry || "",
        user: item.user || "",
        about: item.about || "",
        logo: item.logo || "",
        foundingYear: item.foundingYear,
        size: item.size,
        contractEndDate: item.contractEndDate,
        currency: item.currency || "USD ($)",
        revenueRange: item.revenueRange || "",
        isParentCompany: item.isParentCompany || "",
        parentCompanyName: item.parentCompanyName || "",
        subsidiaries: item.subsidiaries || [],
        linkedinUrl: item.linkedinUrl || "",
        twitterprofileUrl: item.twitterprofileUrl || "",
        instagram: item.instagram || "",
        facebookProfileUrl: item.facebookProfileUrl || "",
        crunchbase: item.crunchbase || "",
        technologyStack: item.technologyStack || [],
        source: item.source || "",
        confidenceScore: item.confidenceScore,
        verified: item.verified || false,
      });
    }
  }, [item]);

  // Helper function to count filled fields
  const countFilledFields = (fields: (string | number | boolean | RowValueMultipleDto[] | null | undefined)[]): number => {
    return fields.filter(field => {
      if (Array.isArray(field)) return field.length > 0;
      if (typeof field === 'boolean') return true; // Booleans are always considered filled
      return field !== null && field !== undefined && field !== '';
    }).length;
  };

  // Count filled fields for each section
  const basicInfoFilled = countFilledFields([
    formData.domainName, formData.name, formData.accounttype, formData.website,
    formData.industry, formData.subIndustry, formData.user, formData.about, formData.logo
  ]);

  const companySizeFilled = countFilledFields([
    formData.foundingYear, formData.size, formData.contractEndDate, formData.currency,
    formData.revenueRange, formData.isParentCompany, formData.parentCompanyName, formData.subsidiaries
  ]);

  const socialLinksFilled = countFilledFields([
    formData.linkedinUrl, formData.twitterprofileUrl, formData.instagram,
    formData.facebookProfileUrl, formData.crunchbase
  ]);

  const technographicsFilled = countFilledFields([formData.technologyStack]);

  const dataManagementFilled = countFilledFields([
    formData.source, formData.confidenceScore, formData.verified
  ]);

  // Extract domain from URL
  const extractDomain = (url: string): string => {
    if (!url) return "";
    try {
      let domain = url.replace(/^https?:\/\//, "").replace(/^www\./, "");
      domain = domain.split("/")[0].split(":")[0];
      return domain.toLowerCase();
    } catch {
      return url;
    }
  };

  // Validate if domain is complete and valid for API call
  const isValidDomainForAPI = (domain: string): boolean => {
    if (!domain || domain.length < 4) return false; // Minimum domain length

    // Check if domain has at least one dot and valid TLD
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)+$/;
    if (!domainRegex.test(domain)) return false;

    // Check if it has a valid TLD (at least 2 characters after the last dot)
    const parts = domain.split('.');
    if (parts.length < 2) return false;

    const tld = parts[parts.length - 1];
    if (tld.length < 2) return false;

    // Don't call API for obviously incomplete domains
    const commonIncompletePatterns = [
      /\.$/, // ends with dot
      /^\./, // starts with dot
      /\.\.$/, // ends with double dot
      /\s/, // contains spaces
    ];

    for (const pattern of commonIncompletePatterns) {
      if (pattern.test(domain)) return false;
    }

    return true;
  };

  // Fetch company data from Apollo API
  const fetchCompanyData = async (domain: string): Promise<void> => {
    if (!domain) return;

    setIsLoadingAPI(true);
    setApiError(null);

    try {
      const apiUrl = `/api/apollo-domain?domain_url=${domain}`;
      const fetchOptions = {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        mode: 'cors' as RequestMode,
      };

      const response = await fetch(apiUrl, fetchOptions);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API request failed: ${response.status} - ${response.statusText}. Response: ${errorText}`);
      }

      const responseText = await response.text();
      const data: ScrapAgentResponse = JSON.parse(responseText);

      if (!data || !data.basicInformation) {
        throw new Error('Invalid API response structure');
      }

      // Auto-fill form data from API response
      setFormData(prev => ({
        ...prev,
        name: data.basicInformation?.company_name || prev.name,
        website: data.basicInformation?.website_url || prev.website,
        industry: data.basicInformation?.industry || prev.industry,
        subIndustry: data.basicInformation?.sub_industry || prev.subIndustry,
        about: data.basicInformation?.about || prev.about,
        logo: data.basicInformation?.logo || prev.logo,
        foundingYear: data.companyInfo?.founding_year || prev.foundingYear,
        size: data.companyInfo?.size_of_company || prev.size,
        revenueRange: data.companyInfo?.revenue_range || prev.revenueRange,
        parentCompanyName: data.companyInfo?.parent_company || prev.parentCompanyName,
        subsidiaries: data.companyInfo?.subsidiaries ?
          data.companyInfo.subsidiaries
            .filter((sub) => sub && sub.trim() !== '') // Filter out null/undefined/empty values
            .map((sub, index) => ({
              id: undefined,
              order: index,
              value: sub
            })) : prev.subsidiaries,
        linkedinUrl: data.socialLinks?.linkedin_url || prev.linkedinUrl,
        twitterprofileUrl: data.socialLinks?.twitter_url || prev.twitterprofileUrl,
        instagram: data.socialLinks?.instagram_url || prev.instagram,
        facebookProfileUrl: data.socialLinks?.facebook_url || prev.facebookProfileUrl,
        crunchbase: data.socialLinks?.crunchbase_url || prev.crunchbase,
        technologyStack: data.technographics ?
          data.technographics
            .filter((tech) => tech && tech.trim() !== '') // Filter out null/undefined/empty values
            .map((tech, index) => ({
              id: undefined,
              order: index,
              value: tech
            })) : prev.technologyStack,
      }));
    } catch (error) {
      // Error handling with specific CORS detection
      if (error instanceof TypeError && error.message.includes('CORS')) {
        setApiError("CORS issue detected. Please configure CORS on your API server.");
      } else if (error instanceof Error) {
        setApiError(`API Error: ${error.message}`);
      } else {
        setApiError("Failed to fetch company data. Please try again.");
      }
    } finally {
      setIsLoadingAPI(false);
    }
  };

  // Immediate input handler for domain field (prevents typing interruption)
  const handleDomainInputChange = (value: string) => {
    // Update the input value immediately for smooth typing
    setDomainInputValue(value);

    // Clear existing timer
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Set new timer for form data update and API call
    const timer = setTimeout(() => {
      // Update form data after delay
      setFormData(prev => ({ ...prev, domainName: value }));

      // Call Apollo API only if domain is complete and valid
      const domain = extractDomain(value);
      if (domain && isValidDomainForAPI(domain)) {
        fetchCompanyData(domain);
      }
    }, 800); // Increased delay to 800ms for better user experience

    setDebounceTimer(timer);
  };

  // Legacy handler for backward compatibility
  const handleDomainChange = (value: string) => {
    handleDomainInputChange(value);
  };

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  function onDelete() {
    confirmDelete.current?.show(t("shared.confirmDelete"), t("shared.delete"), t("shared.cancel"), t("shared.warningCannotUndo"));
  }
  function onDeleteConfirmed() {
    const form = new FormData();
    form.set("action", "delete");
    submit(form, {
      method: "post",
    });
  }
  function isDisabled() {
    if (isUpdating && !canUpdate) {
      return true;
    }
    if (!isUpdating && !isCreating) {
      return true;
    }
  }
  return (
    <Form key={!isDisabled() ? "enabled" : "disabled"} method="post" className="space-y-4">
      {item ? <input name="action" value="edit" hidden readOnly /> : <input name="action" value="create" hidden readOnly />}

      <div className="space-y-4">
        {/* API Loading/Error Messages */}
        {isLoadingAPI && (
          <div className="text-blue-600 text-sm">
            Loading company data...
          </div>
        )}
        {apiError && (
          <div className="text-red-600 text-sm">
            {apiError}
          </div>
        )}

        {/* Basic Information Section */}
        <InputGroup
          title="Basic Information"
          totalFields={9}
          filled={basicInfoFilled}
          defaultOpen={true}
          showFilled={true}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="relative">
              <InputText
                name="domainName"
                title={t("Domain Name")}
                required
                autoFocus
                disabled={isDisabled()}
                value={domainInputValue}
                setValue={(value) => handleDomainInputChange(typeof value === 'function' ? value(domainInputValue) : value)}
                placeholder="e.g., linkfields.com"
                maxLength={100}
                pattern="^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$"
              />
              {/* Domain validation feedback */}
              {domainInputValue && !isLoadingAPI && (
                <div className="mt-1 text-xs">
                  {isValidDomainForAPI(extractDomain(domainInputValue)) ? (
                    <span className="text-green-600">✓ Valid domain - company data will be fetched automatically</span>
                  ) : (
                    <span className="text-gray-500">Enter a complete domain name (e.g., company.com) to auto-fill company data</span>
                  )}
                </div>
              )}
              {isLoadingAPI && (
                <div className="absolute right-3 top-8 flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-xs text-blue-600">Fetching data...</span>
                </div>
              )}
              {apiError && (
                <div className="absolute right-3 top-8 flex items-center">
                  <span className="text-xs text-red-600">⚠️ {apiError}</span>
                </div>
              )}
            </div>
            <InputText
              name="name"
              title={t("Company Name ")}
              required
              disabled={isDisabled()}
              value={formData.name}
              setValue={(value) => setFormData(prev => ({ ...prev, name: typeof value === 'function' ? value(prev.name) : value }))}
              placeholder="e.g., Google, Amazon etc.."
              maxLength={60}
              pattern="^[A-Za-z0-9]+(?: [A-Za-z0-9]+)*$"
            />
            <InputSelect
              name="accounttype"
              title={t("Account Type")}
              disabled={isDisabled()}
              value={formData.accounttype}
              setValue={(value) => setFormData(prev => ({ ...prev, accounttype: String(typeof value === 'function' ? value(prev.accounttype) : value) }))}
              options={[
                { name: "Client", value: "Client", color: Colors.UNDEFINED },
                { name: "Recruiter", value: "Recruiter", color: Colors.UNDEFINED },
                { name: "Partner", value: "Partner", color: Colors.UNDEFINED }
              ]}
              withColors={false}
            />
            <InputTextSubtype
              subtype="url"
              name="website"
              title={t("Website ")}
              disabled={isDisabled()}
              value={formData.website}
              setValue={(value) => setFormData(prev => ({ ...prev, website: typeof value === 'function' ? value(prev.website) : value }))}
              placeholder="e.g., https://xyz.com"
              pattern="/(?:http[s]?:\/\/.)?(?:www\.)?[-a-zA-Z0-9@%._\+~#=]{2,256}\.[a-z]{2,6}\b(?:[-a-zA-Z0-9@:%_\+.~#?&\/\/=]*)/gm"
            />
            <InputText
              name="industry"
              title={t("Industry ")}
              disabled={isDisabled()}
              value={formData.industry}
              setValue={(value) => setFormData(prev => ({ ...prev, industry: typeof value === 'function' ? value(prev.industry) : value }))}
              placeholder="Industry will be auto-filled"
            />
            <InputText
              name="subIndustry"
              title={t("Sub Industry ")}
              disabled={isDisabled()}
              value={formData.subIndustry}
              setValue={(value) => setFormData(prev => ({ ...prev, subIndustry: typeof value === 'function' ? value(prev.subIndustry) : value }))}
              placeholder="e.g., Fintech, EdTech etc.."
              maxLength={200}
              pattern="^[A-Za-z0-9.,'&\-]+(?: [A-Za-z0-9.,'&\-]+)*$"
            />
            <InputSelect
              name="user"
              title={t("Account Owner")}
              disabled={isDisabled()}
              value={formData.user}
              setValue={(value) => setFormData(prev => ({ ...prev, user: String(typeof value === 'function' ? value(prev.user) : value) }))}
              options={[]}
              withColors={false}
            />
          </div>
          <div className="grid grid-cols-1 gap-4">
            <InputText
              name="about"
              title={t("About")}
              disabled={isDisabled()}
              value={formData.about}
              setValue={(value) => setFormData(prev => ({ ...prev, about: typeof value === 'function' ? value(prev.about) : value }))}
              placeholder="e.g., This company works for .."
              rows={5}
              pattern="^[A-Za-z0-9]+(?: [A-Za-z0-9]+)*$ "
            />
            <InputTextSubtype
              subtype="url"
              name="logo"
              title={t("Logo")}
              disabled={isDisabled()}
              value={formData.logo}
              setValue={(value) => setFormData(prev => ({ ...prev, logo: typeof value === 'function' ? value(prev.logo) : value }))}
            />
          </div>
        </InputGroup>

        {/* Company Size & Lifecycle Section */}
        <InputGroup
          title="Company Size & Lifecycle"
          totalFields={8}
          filled={companySizeFilled}
          defaultOpen={true}
          showFilled={true}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <InputNumber
              name="foundingYear"
              title={t("Founding Year ")}
              disabled={isDisabled()}
              value={formData.foundingYear}
              setValue={(value) => setFormData(prev => ({ ...prev, foundingYear: typeof value === 'function' ? value(prev.foundingYear) : value }))}
              max={2099}
            />
            <InputNumber
              name="size"
              title={t("Size of Company")}
              disabled={isDisabled()}
              value={formData.size}
              setValue={(value) => setFormData(prev => ({ ...prev, size: typeof value === 'function' ? value(prev.size) : value }))}
              placeholder="Company size will be auto-filled"
            />
            <InputDate
              name="contractEndDate"
              title={t("Contract End Date ")}
              defaultValue={formData.contractEndDate}
              disabled={isDisabled()}
            />
            <InputSelect
              name="currency"
              title={t("Currency")}
              disabled={isDisabled()}
              value={formData.currency}
              setValue={(value) => setFormData(prev => ({ ...prev, currency: String(typeof value === 'function' ? value(prev.currency) : value) }))}
              options={[
                { name: "INR (₹)", value: "INR (₹)", color: Colors.UNDEFINED },
                { name: "USD ($)", value: "USD ($)", color: Colors.UNDEFINED },
                { name: "EUR (€)", value: "EUR (€)", color: Colors.UNDEFINED },
                { name: "ZAR (R)", value: "ZAR (R)", color: Colors.UNDEFINED }
              ]}
              withColors={false}
            />
            <InputText
              name="revenueRange"
              title={t("Revenue Range")}
              disabled={isDisabled()}
              value={formData.revenueRange}
              setValue={(value) => setFormData(prev => ({ ...prev, revenueRange: typeof value === 'function' ? value(prev.revenueRange) : value }))}
              placeholder="Revenue range will be auto-filled"
              readOnly
            />
            <InputSelect
              name="isParentCompany"
              title={t("Is Parent Company")}
              disabled={isDisabled()}
              value={formData.isParentCompany}
              setValue={(value) => setFormData(prev => ({ ...prev, isParentCompany: String(typeof value === 'function' ? value(prev.isParentCompany) : value) }))}
              options={[
                { name: "Yes", value: "Yes", color: Colors.UNDEFINED },
                { name: "No", value: "No", color: Colors.UNDEFINED }
              ]}
              withColors={false}
            />
            <InputText
              name="parentCompanyName"
              title={t("Parent Company Name")}
              disabled={isDisabled()}
              value={formData.parentCompanyName}
              setValue={(value) => setFormData(prev => ({ ...prev, parentCompanyName: typeof value === 'function' ? value(prev.parentCompanyName) : value }))}
              placeholder="e.g., Alphabet Inc"
              maxLength={60}
              pattern="^[A-Za-z0-9]+(?: [A-Za-z0-9]+)*$"
            />
          </div>
          <div className="grid grid-cols-1 gap-4">
            <InputMultiText
              name="subsidiaries"
              title={t("Subsidiaries")}
              disabled={isDisabled()}
              separator="Enter"
              value={formData.subsidiaries || []}
              placeholder="Press Enter to add each subsidiary"
            />
          </div>
        </InputGroup>

        {/* Social & Web Links Section */}
        <InputGroup
          title="Social & Web Links"
          totalFields={5}
          filled={socialLinksFilled}
          defaultOpen={true}
          showFilled={true}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputTextSubtype
              subtype="url"
              name="linkedinUrl"
              title={t("Linkedin")}
              disabled={isDisabled()}
              value={formData.linkedinUrl}
              setValue={(value) => setFormData(prev => ({ ...prev, linkedinUrl: typeof value === 'function' ? value(prev.linkedinUrl) : value }))}
              placeholder="e.g., https://linkedin.com/company/example"
            />
            <InputTextSubtype
              subtype="url"
              name="twitterprofileUrl"
              title={t("Twitter")}
              disabled={isDisabled()}
              value={formData.twitterprofileUrl}
              setValue={(value) => setFormData(prev => ({ ...prev, twitterprofileUrl: typeof value === 'function' ? value(prev.twitterprofileUrl) : value }))}
              placeholder="e.g., https://twitter.com/example"
            />
            <InputTextSubtype
              subtype="url"
              name="instagram"
              title={t("Instagram")}
              disabled={isDisabled()}
              value={formData.instagram}
              setValue={(value) => setFormData(prev => ({ ...prev, instagram: typeof value === 'function' ? value(prev.instagram) : value }))}
              placeholder="e.g., https://instagram.com/example"
            />
            <InputTextSubtype
              subtype="url"
              name="facebookProfileUrl"
              title={t("Facebook")}
              disabled={isDisabled()}
              value={formData.facebookProfileUrl}
              setValue={(value) => setFormData(prev => ({ ...prev, facebookProfileUrl: typeof value === 'function' ? value(prev.facebookProfileUrl) : value }))}
              placeholder="e.g., https://facebook.com/example"
            />
            <InputTextSubtype
              subtype="url"
              name="crunchbase"
              title={t("Crunchbase")}
              disabled={isDisabled()}
              value={formData.crunchbase}
              setValue={(value) => setFormData(prev => ({ ...prev, crunchbase: typeof value === 'function' ? value(prev.crunchbase) : value }))}
              placeholder="e.g., https://crunchbase.com/organization/example"
            />
          </div>
        </InputGroup>

        {/* Technographics Section */}
        <InputGroup
          title="Technographics"
          totalFields={1}
          filled={technographicsFilled}
          defaultOpen={true}
          showFilled={true}
        >
          <div className="grid grid-cols-1 gap-4">
            <InputMultiText
              name="technologyStack"
              title={t("Technology Stack")}
              disabled={isDisabled()}
              separator="Enter"
              value={formData.technologyStack || []}
              placeholder="Press Enter to add each technology"
            />
          </div>
        </InputGroup>

        {/* Data Management Section */}
        <InputGroup
          title="Data Management"
          totalFields={3}
          filled={dataManagementFilled}
          defaultOpen={true}
          showFilled={true}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <InputSelect
              name="source"
              title={t("Source")}
              disabled={isDisabled()}
              value={formData.source}
              setValue={(value) => setFormData(prev => ({ ...prev, source: String(typeof value === 'function' ? value(prev.source) : value) }))}
              options={[
                { name: "Apollo", value: "Apollo", color: Colors.UNDEFINED },
                { name: "Manual", value: "Manual", color: Colors.UNDEFINED },
                { name: "ZoomInfo", value: "ZoomInfo", color: Colors.UNDEFINED },
                { name: "LinkedIn", value: "LinkedIn", color: Colors.UNDEFINED }
              ]}
              withColors={false}
            />
            <InputNumber
              name="confidenceScore"
              title={t("Confidence Score")}
              disabled={isDisabled()}
              value={formData.confidenceScore}
              setValue={(value) => setFormData(prev => ({ ...prev, confidenceScore: typeof value === 'function' ? value(prev.confidenceScore) : value }))}
              step="0.1"
            />
            <InputCheckbox
              name="verified"
              title={t("Verified")}
              asToggle
              disabled={isDisabled()}
              value={formData.verified}
              setValue={(value) => setFormData(prev => ({ ...prev, verified: typeof value === 'function' ? value(prev.verified) : value }))}
            />
          </div>
        </InputGroup>

        {/* Hidden inputs to ensure controlled form data gets submitted */}
        <input type="hidden" name="domainName" value={formData.domainName} />
        <input type="hidden" name="name" value={formData.name} />
        <input type="hidden" name="accounttype" value={formData.accounttype} />
        <input type="hidden" name="website" value={formData.website} />
        <input type="hidden" name="industry" value={formData.industry} />
        <input type="hidden" name="subIndustry" value={formData.subIndustry} />
        <input type="hidden" name="user" value={formData.user} />
        <input type="hidden" name="about" value={formData.about} />
        <input type="hidden" name="logo" value={formData.logo} />
        <input type="hidden" name="foundingYear" value={formData.foundingYear || ""} />
        <input type="hidden" name="size" value={formData.size || ""} />
        <input type="hidden" name="currency" value={formData.currency} />
        <input type="hidden" name="revenueRange" value={formData.revenueRange} />
        <input type="hidden" name="isParentCompany" value={formData.isParentCompany} />
        <input type="hidden" name="parentCompanyName" value={formData.parentCompanyName} />
        <input type="hidden" name="subsidiaries" value={JSON.stringify(formData.subsidiaries)} />
        <input type="hidden" name="linkedinUrl" value={formData.linkedinUrl} />
        <input type="hidden" name="twitterprofileUrl" value={formData.twitterprofileUrl} />
        <input type="hidden" name="instagram" value={formData.instagram} />
        <input type="hidden" name="facebookProfileUrl" value={formData.facebookProfileUrl} />
        <input type="hidden" name="crunchbase" value={formData.crunchbase} />
        <input type="hidden" name="technologyStack" value={JSON.stringify(formData.technologyStack)} />
        <input type="hidden" name="source" value={formData.source} />
        <input type="hidden" name="confidenceScore" value={formData.confidenceScore || ""} />
        <input type="hidden" name="verified" value={formData.verified.toString()} />
      </div>

      {(isCreating || (isUpdating && canUpdate)) && (
        <div className="flex justify-between space-x-2">
          <div>
            {canDelete && (
              <ButtonSecondary disabled={navigation.state !== "idle"} destructive onClick={onDelete}>
                {t("shared.delete")}
              </ButtonSecondary>
            )}
          </div>
          <div className="flex space-x-2">
            {onCancel && <ButtonSecondary onClick={onCancel}>{t("shared.cancel")}</ButtonSecondary>}
            <ButtonPrimary disabled={navigation.state !== "idle"} type="submit">
              {item ? t("shared.saveDetails") : t("shared.saveDetails")}
            </ButtonPrimary>
          </div>
        </div>
      )}

      <ConfirmModal ref={confirmDelete} onYes={onDeleteConfirmed} />
      <ActionResultModal actionData={actionData} showSuccess={false} />
    </Form>
  );
}